import { fetcher } from '@/api';
import { blogPostApi } from '@/api/blog/post';

// Create method connect to axios
export const categoryApi = {
  get: async <T = any>({
    params,
    slug = ''
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `categories/${slug}` : 'categories';
    return fetcher<T>({ endpoint: path, params });
  }
};
// Create helper function to fetch blog posts filtered by a specific category

export const fetchCategoryBySlug = async (slug: string) => {
  try {
    return await categoryApi.get({
      slug: slug
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    throw error;
  }
};

export const fetchBlogsByCategory = async (categoryId: string, page: number, perPage: number) => {
  try {
    return await blogPostApi.get({
      params: {
        'categories[]': categoryId,
        page,
        per_page: perPage
      }
    });
  } catch (error) {
    console.error('Error fetching blogs by category:', error);
    throw error;
  }
};
