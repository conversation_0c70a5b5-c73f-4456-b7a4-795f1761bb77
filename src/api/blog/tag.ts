import { fetcher } from '@/api';
import { blogPostApi } from '@/api/blog/post';

// Create method connect to axios
export const tagApi = {
  get: async <T = any>({
    params,
    slug = ''
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `tags/${slug}` : 'tags';
    return fetcher<T>({ endpoint: path, params });
  }
};

// Create helper function to fetch blog posts filtered by a specific tag
export const fetchBlogsByTag = async (tagId: string, page: number, perPage: number) => {
  try {
    return await blogPostApi.get({
      params: {
        'tag[]': tagId,
        page,
        per_page: perPage
      }
    });
  } catch (error) {
    console.error('Error fetching blogs by tag:', error);
    throw error;
  }
};

export const fetchTagBySlug = async (slug: string) => {
  try {
    return await tagApi.get({
      slug: slug
    });
  } catch (error) {
    console.error('Error fetching tag:', error);
    throw error;
  }
};
