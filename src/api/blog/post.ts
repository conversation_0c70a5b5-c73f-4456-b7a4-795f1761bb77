import { fetcher } from '@/api';
import { TPostNavigation } from '@/types/blog';

export const blogPostApi = {
  get: async <T = any>({
    params,
    slug = ''
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `posts/${slug}` : 'posts';
    return fetcher<T>({ endpoint: path, params, type: 'blog' });
  },

  getNavigation: async (slug: string): Promise<{data: TPostNavigation | null; error: any; meta: any}> => {
    return fetcher<TPostNavigation>({ endpoint: `posts/${slug}/navigate`, type: 'blog' });
  }
};
