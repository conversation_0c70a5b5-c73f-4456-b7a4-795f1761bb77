import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import CategoryBlogGrid from '@/components/Category/CategoryBlogGrid';
import { getLocale, getTranslations } from 'next-intl/server';
import { fetchBlogsByCategory, fetchCategoryBySlug } from '@/api/blog/category';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';
import { generatePageMetadata } from '@/utils/metadata';

interface CategoryPageProps {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

export async function generateMetadata({ params }: CategoryPageProps) {
  const { slug } = await params;
  const t = await getTranslations('pages.categories');
  const locale = await getLocale();

  try {
    const categoryResponse = await fetchCategoryBySlug(slug);
    const categoryName = categoryResponse.data?.name || slug;
    
    return generatePageMetadata({
      title: `${categoryName} | ${t('title', { default: 'Categories' })}`,
      description: `Browse all blog posts in ${categoryName} category.`,
      path: `blog/category/${slug}`,
      locale,
      imageUrl: 'https://cslant.com/images/og-categories.jpg',
      type: 'website'
    });
  } catch (error) {
    return generatePageMetadata({
      title: 'Category Not Found | CSlant',
      description: 'The requested category could not be found.',
      path: `blog/category/${slug}`,
      locale,
      type: 'website'
    });
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const page = Number(searchParamsData.page) || 1;
  const perPage = PAGE_SIZE_12;

  const t = await getTranslations('pages.categories');

  let categoryResponse;
  let blogsResponse;

  try {
    categoryResponse = await fetchCategoryBySlug(slug);
    if (!categoryResponse.data || categoryResponse.error) {
      notFound();
    }

    blogsResponse = await fetchBlogsByCategory(categoryResponse.data.id, page, perPage);
    if (!blogsResponse.data || blogsResponse.error) {
      notFound();
    }
  } catch (error) {
    notFound();
  }

  const categoryName = categoryResponse.data.name || slug;

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={categoryName} />

      <CategoryBlogGrid
        blogs={blogsResponse.data}
        meta={blogsResponse.meta}
        categoryName={categoryName}
      />

      <Footer />
    </>
  );
};
