import React from "react";
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import BlogDetailsContent from '@/components/Blog/BlogDetailsContent';
import { blogPostApi } from '@/api/blog/post';
import { TBlogPost } from '@/types/blog';
import { Metadata } from 'next';
import { getLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface BlogDetailPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: BlogDetailPageProps): Promise<Metadata> {
  const { slug } = await params;
  const locale = await getLocale();
  try {
    const response = await blogPostApi.get<TBlogPost>({ slug });
    const blogPost = response.data;

    if (!blogPost) {
      return {
        title: 'Blog Post Not Found | CSlant',
        description: 'The requested blog post could not be found.',
      };
    }

    const baseUrl = 'https://cslant.com';
    const localizedPath = locale !== 'en' ? `${locale}/blog/${slug}` : `blog/${slug}`;
    const url = `${baseUrl}/${localizedPath}`;
    const title = `${blogPost.name}`;
    const description = blogPost.description || 'Read this blog post on CSlant.';
    const imageUrl = blogPost.image || 'https://cslant.com/images/og-blog.jpg';

    return {
      title: {
        default: title,
        template: '%s | CSlant',
      },
      description: description,
      creator: 'CSlant',
      authors: [{
        name: blogPost.author?.full_name || 'CSlant',
        url: `${baseUrl}`
      }],
      publisher: 'CSlant',
      alternates: {
        canonical: `${baseUrl}/blog/${slug}`,
        languages: {
          en: `${baseUrl}/blog/${slug}`,
          vi: `${baseUrl}/vi/blog/${slug}`,
          'x-default': `${baseUrl}/blog/${slug}`,
        },
      },
      other: {
        'lang': locale
      },
      openGraph: {
        title: title,
        description: description,
        url: url,
        siteName: 'CSlant',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
        locale: locale === 'vi' ? 'vi_VN' : 'en_US',
        type: 'article',
        publishedTime: blogPost.created_at,
        modifiedTime: blogPost.updated_at,
        authors: [blogPost.author?.full_name || 'CSlant'],
        tags: blogPost.tags?.map(tag => tag.name) || [],
      },
      twitter: {
        card: 'summary_large_image',
        title: title,
        description: description,
        images: [imageUrl],
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    return {
      title: 'Blog Post Not Found | CSlant',
      description: 'The requested blog post could not be found.',
    };
  }
}

export default async function Page({ params }: BlogDetailPageProps) {
  const { slug } = await params;

  try {
    const response = await blogPostApi.get<TBlogPost>({ slug });
    const blogPost = response.data;

    if (!blogPost) {
      notFound();
    }

    return (
      <>
        <Navbar />

        <PageBanner pageTitle={''}  className='pt-0' />

        <BlogDetailsContent blogPost={blogPost} />

        <Footer />
      </>
    );
  } catch (error) {
    notFound();
  }
};
