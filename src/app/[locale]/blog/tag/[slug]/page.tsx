import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import TagBlogGrid from '@/components/Tag/TagBlogGrid';
import { getLocale, getTranslations } from 'next-intl/server';
import { fetchBlogsByTag, fetchTagBySlug } from '@/api/blog/tag';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';
import { generatePageMetadata } from '@/utils/metadata';

interface TagPageProps {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

export async function generateMetadata({ params }: TagPageProps) {
  const { slug } = await params;
  const t = await getTranslations('pages.tags');
  const locale = await getLocale();

  try {
    const tagResponse = await fetchTagBySlug(slug);
    const tagName = tagResponse.data?.name || slug;
    
    return generatePageMetadata({
      title: `${tagName} | ${t('title', { default: 'Tag' })}`,
      description: `Browse all blog posts tagged with ${tagName}.`,
      path: `blog/tag/${slug}`,
      locale,
      imageUrl: 'https://cslant.com/images/og-tags.jpg',
      type: 'website'
    });
  } catch (error) {
    return generatePageMetadata({
      title: 'Tag Not Found | CSlant',
      description: 'The requested tag could not be found.',
      path: `blog/tag/${slug}`,
      locale,
      type: 'website'
    });
  }
}

export default async function TagPage({ params, searchParams }: TagPageProps) {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const page = Number(searchParamsData.page) || 1;
  const perPage = PAGE_SIZE_12;

  let tagResponse;
  let blogsResponse;

  try {
    // Fetch tag info
    tagResponse = await fetchTagBySlug(slug);
    if (!tagResponse.data || tagResponse.error) {
      notFound();
    }

    // Fetch blogs by tag
    blogsResponse = await fetchBlogsByTag(tagResponse.data.id, page, perPage);
    if (!blogsResponse.data || blogsResponse.error) {
      notFound();
    }
  } catch (error) {
    notFound();
  }

  const tagName = tagResponse.data.name || slug;

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={tagName} />

      <TagBlogGrid
        blogs={blogsResponse.data}
        meta={blogsResponse.meta}
        tagName={tagName}
      />

      <Footer />
    </>
  );
};
