import { IMetaSEO } from "@/types/app";

export interface IBlogPost extends TResponse {
  data?: TBlogPost[];
}

export interface IBlogPostDetail extends TResponse {
  data: TBlogPost;
}

export type TBlogPostApiMeta = {
  current_page: number;
  from: number;
  last_page: number;
}

type TAuthor = {
  first_name: string;
  last_name: string;
  full_name: string;
  image: string;
  role: string;
};

type TBlogTag = {
  id: number;
  name: string;
  slug: string;
  description: string;
}

type TBlogCategory = {
  id: number;
  name: string;
  slug: string;
  description: string;
}

export type TBlogPost = {
  id: number;
  name: string;
  slug: string;
  description: string;
  image: string;
  categories: TBlogCategory[];
  tags: TBlogTag[];
  author: TAuthor;
  created_at: string;
  updated_at: string;
  content: string
};

export type BlogPostParams = {
  page?: string | number;
  per_page?: string | number;
  limit?: string | number;
};

export type BlogPostLoader = {
  blogPost: IBlogPost;
};

type TResponse = {
  links?: {
    first: string;
    last: string;
    prev: string;
    next: string;
  };
  meta?: TBlogPostApiMeta;
  error?: boolean;
  message?: string | null;
}

export type TDetailLoader = {
  blogPost: IBlogPostDetail;
  meta: IMetaSEO;
}

// Navigation types for previous/next posts
export type TNavigationSlug = {
  id: number;
  key: string;
  reference_id: number;
  reference_type: string;
  prefix: string;
  created_at: string;
  updated_at: string;
}

export type TNavigationPost = {
  id: number;
  name: string;
  description: string;
  slug: TNavigationSlug;
  url: string | null;
  image: string | null;
  created_at: string;
  updated_at: string;
}

export type TPostNavigation = {
  previous: TNavigationPost | null;
  next: TNavigationPost | null;
}

export interface IPostNavigationResponse extends TResponse {
  data: TPostNavigation;
}
