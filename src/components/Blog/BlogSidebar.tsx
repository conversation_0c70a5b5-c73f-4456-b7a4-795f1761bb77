'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { categoryApi } from '@/api/blog/category';
import { tagApi } from '@/api/blog/tag';
import { blogPostApi } from '@/api/blog/post';
import { TCategoryData } from '@/types/blog/category';
import { TTagData } from '@/types/blog/tag';
import { IBlogPost, TBlogPost } from '@/types/blog';
import { useTranslations } from 'next-intl';
import { assets, formatDate } from '@/utils/helper';
import { blogSlug } from '@/utils/blog';
import Loading from '@/components/Loading';

const BlogSidebar = () => {
  const t = useTranslations('pages.blog.sidebar');
  const [categories, setCategories] = useState<TCategoryData[]>([]);
  const [tags, setTags] = useState<TTagData[]>([]);
  const [popularPosts, setPopularPosts] = useState<TBlogPost[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [tagsLoading, setTagsLoading] = useState(true);
  const [popularPostsLoading, setPopularPostsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true);
        const categoriesResponse = await categoryApi.get<TCategoryData[]>({
          params: {
            per_page: 5
          }
        });

        if (categoriesResponse.data) {
          setCategories(categoriesResponse.data);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    const fetchTags = async () => {
      try {
        setTagsLoading(true);
        const tagsResponse = await tagApi.get<TTagData[]>({
          params: {
            per_page: 15
          }
        });

        if (tagsResponse.data) {
          setTags(tagsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setTagsLoading(false);
      }
    };

    const fetchPopularPosts = async () => {
      try {
        setPopularPostsLoading(true);
        const postsResponse = await blogPostApi.get<IBlogPost>({
          params: {
            page: 1,
            per_page: 5
          }
        });

        if (postsResponse.data && Array.isArray(postsResponse.data)) {
          setPopularPosts(postsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching popular posts:', error);
      } finally {
        setPopularPostsLoading(false);
      }
    };

    fetchCategories();
    fetchTags();
    fetchPopularPosts();
  }, []);

  return (
    <>
      <div className="widget-area sticky-sidebar" id="secondary">
        {/*<div className="widget widget_search">*/}
        {/*  <form className="search-form">*/}
        {/*    <label>*/}
        {/*      <input*/}
        {/*        type="search"*/}
        {/*        className="search-field"*/}
        {/*        placeholder="Search..."*/}
        {/*      />*/}
        {/*    </label>*/}
        {/*    <button type="submit">*/}
        {/*      <Icon.Search />*/}
        {/*    </button>*/}
        {/*  </form>*/}
        {/*</div>*/}

        <div className="widget widget_startp_posts_thumb">
          <h3 className="widget-title">{t('popular-posts')}</h3>

          {popularPostsLoading ? (
            <Loading />
          ) : (
            <>
              {popularPosts && popularPosts.length > 0 ? (
                popularPosts.map((post) => (
                  <article key={post.id} className="item">
                    <Link href={blogSlug(post.slug)} className="thumb">
                      <span
                        className="fullimage cover"
                        role="img"
                        style={{
                          backgroundImage: `url(${assets(post.image)})`
                        }}
                      ></span>
                    </Link>

                    <div className="info">
                      <time>{formatDate(post.created_at)}</time>
                      <h4 className="title usmall">
                        <Link href={blogSlug(post.slug)}>
                          {post.name}
                        </Link>
                      </h4>
                    </div>

                    <div className="clear"></div>
                  </article>
                ))
              ) : (
                <div className="no-posts">
                  <span>{t('no-popular-posts')}</span>
                </div>
              )}
            </>
          )}
        </div>

        <div className="widget widget_categories">
          <h3 className="widget-title">{t('categories')}</h3>

          {categoriesLoading ? (
            <Loading />
          ) : (
            <ul className='d-flex gap-3 flex-wrap'>
              {categories && categories.length > 0 ? (
                categories.map((category) => (
                  <li key={category.id}>
                    <Link href={`/blog/category/${category.slug}`}>
                      {category.name}
                    </Link>
                  </li>
                ))
              ) : (
                <li>
                  <span>{t('no-categories')}</span>
                </li>
              )}
            </ul>
          )}
        </div>

        <div className="widget widget_tag_cloud">
          <h3 className="widget-title">{t('tags')}</h3>

          {tagsLoading ? (
            <Loading />
          ) : (
            <div className="tagcloud">
              {tags && tags.length > 0 ? (
                tags.map((tag) => (
                  <Link key={tag.id} href={`/blog/tag/${tag.slug}`}>
                    {tag.name}{' '}{'('}
                    {tag.posts_count && (
                      <span className="tag-link-count">{tag.posts_count}</span>
                    )}
                    {')'}
                  </Link>
                ))
              ) : (
                <span>{t('no-tags')}</span>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default BlogSidebar;
