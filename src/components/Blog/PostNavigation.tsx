'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLocale, useTranslations } from 'next-intl';
import { blogPostApi } from '@/api/blog/post';
import { TPostNavigation } from '@/types/blog';
import { assets, formatDate, generatePlaceholderImage } from '@/utils/helper';

interface PostNavigationProps {
  currentSlug: string;
}

const PostNavigation: React.FC<PostNavigationProps> = ({ currentSlug }) => {
  const [navigation, setNavigation] = useState<TPostNavigation | null>(null);
  const t = useTranslations('pages.blog');

  const [navigationLoading, setNavigationLoading] = useState(true);

  // Fetch navigation data
  useEffect(() => {
    const fetchNavigation = async () => {
      try {
        setNavigationLoading(true);
        const response = await blogPostApi.getNavigation(currentSlug);
        
        if (response.data) {
          setNavigation(response.data);
        }
      } catch (error) {
        console.error('Error fetching post navigation:', error);
      } finally {
        setNavigationLoading(false);
      }
    };

    if (currentSlug) {
      fetchNavigation();
    }
  }, [currentSlug]);

  return (
    <div className="startp-post-navigation">
      {/* Previous Post */}
      {navigationLoading ? (
        <div className="prev-link-wrapper">
          <div className="info-prev-link-wrapper">
            <div className="navigation-loading">
              <span className="image-prev">
                <div className="skeleton-image"></div>
                <span className="post-nav-title">{t('prev')}</span>
              </span>
              <span className="prev-link-info-wrapper">
                <span className="prev-title skeleton-title"></span>
                <span className="meta-wrapper">
                  <span className="date-post skeleton-date"></span>
                </span>
              </span>
            </div>
          </div>
        </div>
      ) : navigation?.previous && navigation.previous.slug?.key ? (
        <div className="prev-link-wrapper">
          <div className="info-prev-link-wrapper">
            <Link
              href={`/blog/${navigation.previous.slug.key}`}>
              <div className="image-prev">
                <Image
                  src={navigation.previous.image ? assets(navigation.previous.image) : generatePlaceholderImage({
                    x: 100,
                    y: 81
                  })}
                  alt={navigation.previous.name || 'Previous post'}
                  width={100}
                  height={81}
                />
                <span className="post-nav-title">Prev</span>
              </div>

              <span className="prev-link-info-wrapper line-clamp-2">
                <span
                  className="font-medium font-[17px] line-clamp-2">
                  {navigation.previous.name || 'Previous post'}
                </span>
                <span className="meta-wrapper">
                  <span className="date-post">
                    {formatDate(navigation.previous.created_at)}
                  </span>
                </span>
              </span>
            </Link>
          </div>
        </div>
      ) : null}

      {/* Next Post */}
      {navigationLoading ? (
        <div className="next-link-wrapper">
          <div className="info-next-link-wrapper">
            <div className="navigation-loading">
              <span className="next-link-info-wrapper">
                <span className="next-title skeleton-title"></span>
                <span className="meta-wrapper">
                  <span className="date-post skeleton-date"></span>
                </span>
              </span>
              <span className="image-next">
                <div className="skeleton-image"></div>
                <span className="post-nav-title">Next</span>
              </span>
            </div>
          </div>
        </div>
      ) : navigation?.next && navigation.next.slug?.key ? (
        <div className="next-link-wrapper">
          <div className="info-next-link-wrapper">
            <Link
              href={`/blog/${navigation.next.slug.key}`}>
              <span className="next-link-info-wrapper">
                <span
                  className="font-medium font-[17px] line-clamp-2">
                  {navigation.next.name || 'Next post'}
                </span>
                <span className="meta-wrapper">
                  <span className="date-post">
                    {formatDate(navigation.next.created_at)}
                  </span>
                </span>
              </span>

              <div className="image-next">
                <Image
                  src={navigation.next.image ? assets(navigation.next.image) : generatePlaceholderImage({
                    x: 100,
                    y: 81
                  })}
                  alt={navigation.next.name || 'Next post'}
                  width={100}
                  height={81}
                />
                <span className="post-nav-title">{t('next')}</span>
              </div>
            </Link>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default PostNavigation;
